
Ministry of Higher Education
 Ghalib University
Faculty of Computer Science
(Network/Software Department)

Proposal
Project Title 
(                                                                                     )

A proposal submitted in fulfillment of the requirements for the Degree of 
Bachelor in computer Science

Student’s Name:  (                                             )

Department:  (                                                     )

Date of submission:  (                                      )
1.	  Abstract
در این قسمت از پروپوزال معرفی و هدف کلی موضوع خود را بنویسید. 




2.	 Introduction
The rapid advancement of digital technologies has revolutionized educational administration and management systems worldwide. Language and skills training centers, particularly in multilingual and multicultural environments, face unique challenges in managing student enrollment, academic programs, financial transactions, and administrative operations. This monograph presents the development and implementation of a comprehensive Educational Management System (EMS) specifically designed for the Ghalib Language and Skills Center, addressing the complex requirements of modern educational institutions operating in diverse linguistic environments.

The Ghalib Language and Skills Center represents a contemporary educational institution that provides language training and professional skills development programs. The center operates in a multilingual environment, primarily serving Persian-speaking communities while maintaining English language capabilities for broader accessibility. The institution's commitment to excellence in education necessitated the development of a sophisticated management system capable of handling diverse administrative, academic, and financial operations.







3.	 Current  System  (if  exist)

در این قسمت:  اینکه تا به حال در جایی از این سیستم و نرم افزار  استفاده شده است یا خیر؟
مثلا شاید در یک اداره‏ای استفاده شده باشد، توضیحات اش را بنویسید و یا شاید در کدام مرکز دیگری از این سیستم استفاده شده باشد، توضیحات ارائه بدهید.


4.	 Problem   Statement  /  Requirements   of  System
در این قسمت:  اینکه شما نیازمندی های سیستمی را که میخواهید استفاده کنید، مشخص میکنید، یعنی نیازمندی هاردویری آن را مشخص کنید.
مثلاً: شاید گفته شده که برای راه اندازی سیستم مد نظر  پراسسور کمپیوتر باید حداقل Core i3،  یا حافظه RAM به اندازه فلان مقدار و  غیره ....
مختصر توضیحات بنویسید.
5.	 Software   Specifications
در این قسمت: درباره خود نرم افزار یا package که میخواهید استفاده کنید توضیحات بدهید.
یعنی شما میخواهید از نرم افزار NFC استفاده کنید، مختصر توضیحاتی درباره NFC بنویسید، که برای خواننده دقیق شناسانده شود.


6.	Reasons   of   Choice

در قسمت Reason of Choice: اینکه به چه دلیل این موضوع را انتخاب کرده اید، و یا دلیل انتخاب این موضوع تان چیست؟ آیا در کدام جایی نیازمندی احساس شده است، و آیا در جایی کاربرد داشته است. 
دلایل تان را بنویسید.

Student Name: (                                             )
ID NO:
Semester:
Department:
Contact NO:
E-Mail:


Signature:
