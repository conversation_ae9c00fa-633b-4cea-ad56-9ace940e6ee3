
Ministry of Higher Education
 Ghalib University
Faculty of Computer Science
(Network/Software Department)

Proposal
Project Title
(Ghalib Language and Skills Center Management System)

A proposal submitted in fulfillment of the requirements for the Degree of 
Bachelor in computer Science

Student’s Name:  (                                             )

Department:  (                                                     )

Date of submission:  (                                      )
1.	  Abstract

This project presents the development of a comprehensive Educational Management System for the Ghalib Language and Skills Center. The system is designed to streamline administrative operations, student enrollment processes, financial management, and academic program coordination. Built using ASP.NET Core Blazor Server technology with SQL Server database, the system provides a multilingual interface supporting both Persian and English languages. The primary objective is to enhance operational efficiency, improve data accuracy, and provide real-time management capabilities for educational institutions. The system includes modules for student management, program administration, payment processing, staff management, and comprehensive reporting features with role-based access control.

2.	 Introduction
The rapid advancement of digital technologies has revolutionized educational administration and management systems worldwide. Language and skills training centers, particularly in multilingual and multicultural environments, face unique challenges in managing student enrollment, academic programs, financial transactions, and administrative operations. This monograph presents the development and implementation of a comprehensive Educational Management System (EMS) specifically designed for the Ghalib Language and Skills Center, addressing the complex requirements of modern educational institutions operating in diverse linguistic environments.

The Ghalib Language and Skills Center represents a contemporary educational institution that provides language training and professional skills development programs. The center operates in a multilingual environment, primarily serving Persian-speaking communities while maintaining English language capabilities for broader accessibility. The institution's commitment to excellence in education necessitated the development of a sophisticated management system capable of handling diverse administrative, academic, and financial operations.







3.	 Current  System  (if  exist)

Currently, most language and skills training centers rely on traditional manual processes or basic spreadsheet-based systems for managing their operations. These existing approaches typically involve:

- Paper-based student registration and enrollment forms
- Manual tracking of payments and financial records
- Separate systems for different departments without integration
- Limited reporting capabilities and data analysis tools
- No centralized database for student and program information

Some educational institutions use generic Student Information Systems (SIS) that are not specifically designed for language centers and lack features such as multilingual support, specialized payment structures for language courses, and cultural considerations for Persian-speaking environments. The proposed Ghalib Language and Skills Center Management System addresses these limitations by providing a comprehensive, integrated solution specifically tailored for language and skills training institutions.


4.	 Problem   Statement  /  Requirements   of  System

**Problem Statement:**
The Ghalib Language and Skills Center faces challenges in managing student data, course enrollment, payment processing, and administrative operations efficiently. Manual processes lead to data inconsistencies, time-consuming administrative tasks, and limited reporting capabilities.

**System Requirements:**

**Hardware Requirements:**
- Server: Minimum Intel Core i5 processor or equivalent
- RAM: Minimum 8GB, recommended 16GB for optimal performance
- Storage: Minimum 500GB SSD for database and application files
- Network: Stable internet connection with minimum 100 Mbps bandwidth
- Client machines: Any modern computer with web browser support

**Software Requirements:**
- Operating System: Windows Server 2019 or later / Linux distributions
- Database: Microsoft SQL Server 2019 or later
- Web Server: IIS 10.0 or later / Apache/Nginx for Linux
- Framework: .NET 9.0 Runtime
- Browser: Modern web browsers (Chrome, Firefox, Edge, Safari)
5.	 Software   Specifications

**Primary Technologies:**

**ASP.NET Core 9.0 with Blazor Server:**
ASP.NET Core is a cross-platform, high-performance framework for building modern web applications. Blazor Server enables interactive web UI development using C# instead of JavaScript, providing real-time updates and seamless user experience.

**Microsoft SQL Server:**
A robust relational database management system that provides data integrity, security, and scalability. SQL Server offers advanced features like stored procedures, triggers, and comprehensive backup solutions.

**Dapper ORM:**
A lightweight Object-Relational Mapping (ORM) framework that provides fast data access with minimal overhead. Dapper offers excellent performance for database operations while maintaining simplicity.

**ASP.NET Core Identity:**
A comprehensive authentication and authorization system that provides user management, role-based access control, and security features including password hashing and account lockout protection.

**Bootstrap Framework:**
A responsive CSS framework that ensures the application works seamlessly across different devices and screen sizes, providing consistent user interface components.

**TabBlazor Components:**
Advanced UI component library that enhances the user interface with professional-looking tables, forms, and interactive elements specifically designed for business applications.


6.	Reasons   of   Choice

**Educational Sector Digitization Need:**
The education sector, particularly language and skills training centers, requires modern digital solutions to improve efficiency and service quality. Traditional manual processes are time-consuming and prone to errors.

**Multilingual Environment Requirements:**
The Ghalib Language and Skills Center serves a diverse community requiring both Persian and English language support. Existing systems lack proper multilingual capabilities and cultural considerations for Persian-speaking users.

**Operational Efficiency Improvement:**
Manual student registration, payment tracking, and administrative processes create bottlenecks and inefficiencies. An integrated management system can significantly reduce administrative workload and improve accuracy.

**Data Management and Security:**
Centralized data management with proper security measures ensures student information privacy and enables better decision-making through comprehensive reporting and analytics.

**Scalability and Future Growth:**
The system is designed to accommodate the center's growth and expansion plans, providing a foundation for additional features and increased user capacity.

**Technology Advancement:**
Utilizing modern web technologies like Blazor Server and .NET Core ensures the system remains current with industry standards and provides excellent performance and maintainability.

**Cost-Effective Solution:**
Developing a custom solution specifically tailored to the center's needs provides better value compared to purchasing and customizing generic educational software packages.

Student Name: (                                             )
ID NO:
Semester:
Department:
Contact NO:
E-Mail:


Signature:
